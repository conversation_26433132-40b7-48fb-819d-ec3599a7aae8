
import React, { useState, useCallback } from 'react';
import { Plant, GeminiDiagnosis } from '@/types';
import { useAuth } from '@/hooks/useAuth';
import { uploadImage, analyzePlantImages, addDiagnosticRecord } from '@/services/api';
import { Button } from '@/components/common/Button';
import { Spinner } from '@/components/common/Spinner';
import { CameraIcon, XMarkIcon } from '@/components/common/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { serverTimestamp, Timestamp } from 'firebase/firestore';
import imageCompression from 'browser-image-compression';

interface NewDiagnosticProps {
  plant: Plant;
  onFinish: () => void;
}

type Stage = 'upload' | 'analyzing' | 'result';

export const NewDiagnostic: React.FC<NewDiagnosticProps> = ({ plant, onFinish }) => {
  const { user } = useAuth();
  const [stage, setStage] = useState<Stage>('upload');
  const [files, setFiles] = useState<File[]>([]);
  const [previews, setPreviews] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<GeminiDiagnosis | null>(null);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
        const selectedFiles = Array.from(event.target.files);
        
        const options = {
            maxSizeMB: 1,
            maxWidthOrHeight: 1920,
            useWebWorker: true
        }

        try {
            const compressedFiles = await Promise.all(selectedFiles.map(file => imageCompression(file, options)));
            setFiles(prev => [...prev, ...compressedFiles]);

            const newPreviews = compressedFiles.map(file => URL.createObjectURL(file));
            setPreviews(prev => [...prev, ...newPreviews]);

        } catch (error) {
            console.error(error);
            setError("Failed to compress images. Please try again.");
        }
    }
  };

  const removeImage = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
    setPreviews(previews.filter((_, i) => i !== index));
  };
  
  const handleAnalyze = async () => {
    if (!user || files.length === 0) return;

    setIsLoading(true);
    setStage('analyzing');
    setError(null);

    try {
      const imageUrls = await Promise.all(
        files.map(file => uploadImage(file, `diagnostics/${user.uid}/${plant.id}/${Date.now()}_${file.name}`))
      );

      const base64Images = await Promise.all(
          files.map(file => imageCompression.getDataUrlFromFile(file).then(url => url.split(',')[1]))
      );

      const analysisResult = await analyzePlantImages(base64Images, plant.name);
      
      setResult(analysisResult);
      setStage('result');

    } catch (err) {
      console.error(err);
      setError('An error occurred during analysis. Please try again.');
      setStage('upload');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveResult = async () => {
      if(!user || !plant || !result) return;
      setIsLoading(true);
      try {
        const imageUrls = await Promise.all(
            files.map(file => uploadImage(file, `diagnostics/${user.uid}/${plant.id}/${Date.now()}_${file.name}`))
        );

        const newRecord = {
            userId: user.uid,
            plantId: plant.id,
            timestamp: serverTimestamp(),
            imageUrls,
            diagnosis: result,
        } as any;

        if(result.treatmentPlan.treatmentFrequencyDays > 0) {
            const nextDate = new Date();
            nextDate.setDate(nextDate.getDate() + result.treatmentPlan.treatmentFrequencyDays);
            newRecord.nextTreatmentDate = Timestamp.fromDate(nextDate);
        }

        await addDiagnosticRecord(user.uid, plant.id, newRecord);
        onFinish();

      } catch (error) {
          console.error("Failed to save result", error);
          setError("Could not save the diagnostic record.");
      } finally {
          setIsLoading(false);
      }
  };

  const renderContent = () => {
    switch (stage) {
      case 'upload':
        return (
          <>
            <h2 className="text-3xl font-bold text-white mb-2">New Diagnosis for {plant.name}</h2>
            <p className="text-[#E0E0E0] mb-6">Upload photos of your plant. For best results, include a clear picture of the affected area.</p>
            <div className="p-8 border-2 border-dashed border-gray-600 rounded-2xl text-center bg-[#100f1c]">
              <input type="file" id="file-upload" className="hidden" multiple accept="image/*" onChange={handleFileChange} />
              <label htmlFor="file-upload" className="cursor-pointer">
                <CameraIcon className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-white font-semibold">Click to upload photos</p>
                <p className="text-sm text-gray-400">or drag and drop</p>
              </label>
            </div>
            <AnimatePresence>
                {previews.length > 0 && (
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mt-6">
                    {previews.map((src, index) => (
                        <motion.div key={index} layout className="relative">
                            <img src={src} alt={`preview ${index}`} className="rounded-lg w-full h-32 object-cover"/>
                            <button onClick={() => removeImage(index)} className="absolute top-1 right-1 bg-black/50 rounded-full p-1 text-white">
                                <XMarkIcon className="w-4 h-4" />
                            </button>
                        </motion.div>
                    ))}
                    </div>
                )}
            </AnimatePresence>
            <div className="mt-8 flex justify-end gap-4">
              <Button variant="secondary" onClick={onFinish}>Cancel</Button>
              <Button onClick={handleAnalyze} disabled={files.length === 0} isLoading={isLoading}>Analyze Images</Button>
            </div>
          </>
        );
      case 'analyzing':
        return (
            <div className="text-center">
                <Spinner size="lg" />
                <h2 className="text-3xl font-bold text-white mt-6">Analyzing...</h2>
                <p className="text-[#E0E0E0] mt-2">FloraSynth is inspecting your plant. This might take a moment.</p>
            </div>
        );
      case 'result':
        return result && (
            <div>
                <h2 className="text-3xl font-bold text-white mb-2">Analysis Complete</h2>
                <p className="text-xl text-white mb-4">Diagnosis: <span className="font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#d385f5] to-[#a364f7]">{result.disease}</span></p>
                <div className="space-y-4 text-[#E0E0E0] p-4 bg-[#100f1c] rounded-lg">
                    <p>{result.description}</p>
                    <h3 className="font-bold text-white text-lg mt-4">Treatment Plan</h3>
                    <ul className="list-disc list-inside space-y-1">
                        {result.treatmentPlan.steps.map((step, i) => <li key={i}>{step}</li>)}
                    </ul>
                    {result.treatmentPlan.treatmentFrequencyDays > 0 && <p className="font-semibold">Repeat treatment every {result.treatmentPlan.treatmentFrequencyDays} days.</p>}
                     <h3 className="font-bold text-white text-lg mt-4">Care Tips</h3>
                     <ul className="list-disc list-inside space-y-1">
                        {result.careTips.map((tip, i) => <li key={i}>{tip}</li>)}
                    </ul>
                </div>
                 <div className="mt-8 flex justify-end gap-4">
                    <Button variant="secondary" onClick={() => setStage('upload')}>Re-upload</Button>
                    <Button onClick={handleSaveResult} isLoading={isLoading}>Save to History</Button>
                </div>
            </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-[#100f1c]/80 backdrop-blur-sm">
      <motion.div 
        initial={{opacity: 0, scale: 0.95}}
        animate={{opacity: 1, scale: 1}}
        className="w-full max-w-3xl bg-[#1c1a31] p-8 rounded-2xl"
      >
        {error && <p className="text-red-400 mb-4 text-center">{error}</p>}
        {renderContent()}
      </motion.div>
    </div>
  );
};
